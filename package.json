{"name": "back-end-acara", "version": "1.0.0", "type": "commonjs", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "bun --watch src/index.ts", "dev:nodemon": "nodemon --exec bun src/index.ts", "docs": "bun src/docs/swagger.ts", "start": "bun src/index.ts", "build": "bun build src/index.ts --outdir dist --target node"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/mongoose": "^5.11.97", "@types/nodemailer": "^6.4.17", "@types/swagger-ui-express": "^4.1.8", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.2", "nodemailer": "^6.9.14", "nodemon": "^3.1.9", "swagger-autogen": "^2.23.7", "swagger-ui-express": "4.6.3", "ts-node": "^10.9.2", "typescript": "^5.4.5", "zod": "^4.1.0"}, "devDependencies": {"@types/ejs": "^3.1.5", "@types/jsonwebtoken": "^9.0.9"}}